# دليل المؤشرات الفنية - نظام Quotex المتقدم

## نظرة عامة

تم تطوير نظام شامل للمؤشرات الفنية يحتوي على 15 مؤشراً فنياً متقدماً مع تحليل البيانات التاريخية والحية.

## المؤشرات المتوفرة (15 مؤشر)

### 1. المتوسطات المتحركة الأسية (EMA)
- **EMA5**: متوسط متحرك أسي لـ 5 فترات
- **EMA10**: متوسط متحرك أسي لـ 10 فترات  
- **EMA21**: متوسط متحرك أسي لـ 21 فترة

### 2. المتوسط المتحرك البسيط (SMA)
- **SMA10**: متوسط متحرك بسيط لـ 10 فترات

### 3. مؤشر القوة النسبية (RSI)
- **RSI5**: مؤشر القوة النسبية لـ 5 فترات
- **RSI14**: مؤشر القوة النسبية لـ 14 فترة

### 4. مؤشر MACD
- **MACD**: تقارب وتباعد المتوسطات (12, 26, 9)
  - خط MACD
  - خط الإشارة
  - الهيستوجرام

### 5. مؤشر الزخم
- **Momentum10**: مؤشر الزخم لـ 10 فترات

### 6. نطاقات بولينجر
- **BollingerBands**: نطاقات بولينجر (20, 2)
  - النطاق العلوي
  - النطاق الأوسط (SMA)
  - النطاق السفلي

### 7. متوسط المدى الحقيقي (ATR)
- **ATR5**: متوسط المدى الحقيقي لـ 5 فترات
- **ATR14**: متوسط المدى الحقيقي لـ 14 فترة

### 8. شموع هايكن آشي
- **HeikenAshi**: شموع هايكن آشي المعدلة
  - فتح هايكن آشي
  - إغلاق هايكن آشي
  - أعلى هايكن آشي
  - أدنى هايكن آشي

### 9. مؤشر Z-Score
- **ZScore**: النتيجة المعيارية لـ 20 فترة

## آلية العمل

### 1. التحليل الأولي
```
عند بدء النظام:
1. جلب البيانات التاريخية لجميع الأزواج
2. حساب جميع المؤشرات للبيانات التاريخية
3. حفظ النتائج في ملفات منفصلة لكل زوج
```

### 2. التحليل المباشر
```
أثناء البث المباشر:
1. تحديث المؤشرات مع كل شمعة حية
2. إعادة حساب المؤشرات عند اكتمال الشمعة
3. حفظ النتائج المحدثة
```

## هيكل البيانات

### ملف المؤشرات (مثال: EURUSD_indicators.json)
```json
{
  "pair": "EURUSD",
  "last_updated": "2025-08-02T10:30:00",
  "candles_count": 200,
  "has_live_data": true,
  "indicators": {
    "EMA5": {
      "indicator": "EMA5",
      "parameters": {"period": 5},
      "values": [1.0850, 1.0852, ...],
      "current": 1.0857,
      "timestamp": 1754094300,
      "datetime": "2025-08-02 10:25:00"
    },
    "RSI14": {
      "indicator": "RSI14",
      "parameters": {"period": 14},
      "values": [45.2, 47.8, ...],
      "current": 52.3,
      "timestamp": 1754094300,
      "datetime": "2025-08-02 10:25:00"
    },
    "MACD": {
      "indicator": "MACD",
      "parameters": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
      "macd_line": [0.0012, 0.0015, ...],
      "signal_line": [0.0010, 0.0013, ...],
      "histogram": [0.0002, 0.0002, ...],
      "current_macd": 0.0018,
      "current_signal": 0.0016,
      "current_histogram": 0.0002
    }
  }
}
```

## المجلدات والملفات

```
data/
├── indicators/
│   ├── EURUSD_indicators.json
│   ├── EURUSD_otc_indicators.json
│   ├── GBPUSD_indicators.json
│   └── ...
└── historical/
    ├── EURUSD.json
    ├── EURUSD_otc.json
    └── ...
```

## الاستخدام البرمجي

### الحصول على مؤشرات زوج معين
```python
from src.indicators.indicators_manager import IndicatorsManager

indicators_manager = IndicatorsManager()

# الحصول على جميع المؤشرات لزوج معين
pair_indicators = indicators_manager.get_pair_indicators("EURUSD")

# الحصول على ملخص القيم الحالية
summary = indicators_manager.get_indicator_summary("EURUSD")
```

### الوصول لمؤشر محدد
```python
# الحصول على قيمة EMA5 الحالية
ema5_current = pair_indicators['indicators']['EMA5']['current']

# الحصول على قيم RSI14 التاريخية
rsi14_values = pair_indicators['indicators']['RSI14']['values']

# الحصول على نطاقات بولينجر الحالية
bb_upper = pair_indicators['indicators']['BollingerBands']['current_upper']
bb_middle = pair_indicators['indicators']['BollingerBands']['current_middle']
bb_lower = pair_indicators['indicators']['BollingerBands']['current_lower']
```

## المميزات المتقدمة

### 1. التحليل المتوازي
- حساب المؤشرات لجميع الأزواج بشكل متوازي
- تحسين الأداء والسرعة

### 2. التحديث المباشر
- تحديث المؤشرات مع البيانات الحية
- إعادة حساب عند اكتمال الشمعة

### 3. مقاومة الأخطاء
- استمرار العمل حتى لو فشل حساب مؤشر معين
- تسجيل الأخطاء دون توقف النظام

### 4. الحفظ الذكي
- حفظ منفصل لكل زوج
- تحديث البيانات عند الحاجة فقط

## الرسائل والحالة

- 📊 بدء تحليل المؤشرات
- ✅ اكتمال حساب المؤشرات
- ⚠️ تحذيرات المؤشرات
- ❌ أخطاء المؤشرات

## الأداء

- **عدد المؤشرات**: 15 مؤشر لكل زوج
- **سرعة الحساب**: متوازي لجميع الأزواج
- **التحديث**: مع كل شمعة مكتملة
- **التخزين**: ملفات JSON منفصلة

هذا النظام يوفر تحليلاً فنياً شاملاً ومتقدماً لجميع أزواج الفوريكس مع إمكانية الوصول السهل للبيانات والنتائج.
