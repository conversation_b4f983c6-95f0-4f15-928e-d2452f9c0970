"""
Z-Score Indicator
Statistical Z-Score calculation
"""

from typing import List, Dict, Any
import statistics
from .base_indicator import BaseIndicator

class ZScore(BaseIndicator):
    """Z-Score with 20 periods"""
    
    def __init__(self):
        super().__init__("ZScore", {"period": 20})
    
    def get_required_periods(self) -> int:
        return 20
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        zscore_values = []
        
        # Calculate Z-Score for each period
        for i in range(19, len(prices)):  # Start from index 19 (20th element)
            # Get the 20 prices for this period
            period_prices = prices[i-19:i+1]
            
            # Calculate mean and standard deviation
            mean_price = statistics.mean(period_prices)
            std_dev = statistics.stdev(period_prices) if len(period_prices) > 1 else 0
            
            # Calculate Z-Score: (current_price - mean) / std_dev
            if std_dev != 0:
                zscore = (prices[i] - mean_price) / std_dev
            else:
                zscore = 0
            
            zscore_values.append(zscore)
        
        return self.format_result({
            'values': zscore_values,
            'current': zscore_values[-1] if zscore_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live Z-Score, we would need the last 19 prices + current live price
        # This is a simplified approach
        current_zscore = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_zscore],
            'current': current_zscore,
            'live': True
        }, live_candle.get('time'))
