"""
MACD Indicator
Moving Average Convergence Divergence
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class MACD(BaseIndicator):
    """MACD with 12, 26, 9 periods"""
    
    def __init__(self):
        super().__init__("MACD", {"fast_period": 12, "slow_period": 26, "signal_period": 9})
    
    def get_required_periods(self) -> int:
        return 35  # 26 + 9 for signal line
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result({}, candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        
        # Calculate EMAs
        ema12 = self.calculate_ema(prices, 12)
        ema26 = self.calculate_ema(prices, 26)
        
        if not ema12 or not ema26:
            return self.format_result({}, candles[-1].get('time'))
        
        # Align EMAs (EMA26 starts later)
        start_index = len(ema12) - len(ema26)
        aligned_ema12 = ema12[start_index:]
        
        # Calculate MACD line
        macd_line = [fast - slow for fast, slow in zip(aligned_ema12, ema26)]
        
        # Calculate Signal line (EMA of MACD)
        signal_line = self.calculate_ema(macd_line, 9)
        
        # Calculate Histogram
        if signal_line:
            histogram_start = len(macd_line) - len(signal_line)
            aligned_macd = macd_line[histogram_start:]
            histogram = [macd - signal for macd, signal in zip(aligned_macd, signal_line)]
        else:
            histogram = []
        
        return self.format_result({
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram,
            'current_macd': macd_line[-1] if macd_line else None,
            'current_signal': signal_line[-1] if signal_line else None,
            'current_histogram': histogram[-1] if histogram else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('macd_line'):
            return self.format_result({}, live_candle.get('time'))
        
        # For live MACD, we would need to recalculate with the live price
        # This is a simplified approach
        current_macd = historical_values.get('current_macd', 0)
        current_signal = historical_values.get('current_signal', 0)
        current_histogram = historical_values.get('current_histogram', 0)
        
        return self.format_result({
            'macd_line': historical_values['macd_line'] + [current_macd],
            'signal_line': historical_values['signal_line'] + [current_signal],
            'histogram': historical_values['histogram'] + [current_histogram],
            'current_macd': current_macd,
            'current_signal': current_signal,
            'current_histogram': current_histogram,
            'live': True
        }, live_candle.get('time'))
