#!/usr/bin/env python3
"""
Main application for Quotex trading system
Connects to Quotex platform and manages forex pairs data and historical data collection
"""

import asyncio
import logging
import signal
import sys
import time
from datetime import datetime
from typing import Optional

from pyquotex.stable_api import Quotex
from src.forex_pairs.pairs_manager import ForexPairsManager
from src.historical_data.historical_manager import HistoricalDataManager
from src.live_data.live_manager import LiveDataManager
from src.indicators.indicators_manager import IndicatorsManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('main.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QuotexTradingSystem:
    """Main trading system class that manages connection and data collection"""

    def __init__(self, email: str = None, password: str = None, lang: str = "en"):
        """
        Initialize the trading system

        Args:
            email: Quotex account email
            password: Quotex account password
            lang: Language preference (en, pt, es)
        """
        self.client = Quotex(
            email=email,
            password=password,
            lang=lang,
            user_data_dir="browser_data"
        )

        self.pairs_manager: Optional[ForexPairsManager] = None
        self.historical_manager: Optional[HistoricalDataManager] = None
        self.live_manager: Optional[LiveDataManager] = None
        self.indicators_manager: Optional[IndicatorsManager] = None
        self.is_connected = False
        self.is_running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print(f"\n🛑 Received shutdown signal {signum}, stopping system...")
        self.is_running = False

        # Create a task to shutdown gracefully
        if hasattr(self, '_shutdown_task') and not self._shutdown_task.done():
            return

        self._shutdown_task = asyncio.create_task(self.shutdown())

    async def connect(self) -> bool:
        """
        Connect to Quotex platform

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            logger.info("Connecting to Quotex platform...")

            # Set account mode to practice (demo)
            self.client.set_account_mode("PRACTICE")

            # Connect to the platform
            check_connect, message = await self.client.connect()

            if check_connect:
                logger.info(f"Successfully connected to Quotex: {message}")
                self.is_connected = True

                # Get and display account information
                profile = await self.client.get_profile()
                balance = await self.client.get_balance()

                logger.info(f"Account: {profile.nick_name}")
                logger.info(f"Balance: {balance} {profile.currency_symbol}")

                return True
            else:
                logger.error(f"Failed to connect to Quotex: {message}")
                return False

        except Exception as e:
            logger.error(f"Error during connection: {e}")
            return False

    async def initialize_managers(self):
        """Initialize the data managers"""
        try:
            print("🔧 Initializing data managers...")

            # Initialize forex pairs manager
            self.pairs_manager = ForexPairsManager(self.client)

            # Initialize historical data manager
            self.historical_manager = HistoricalDataManager(self.client)

            # Initialize live data manager
            self.live_manager = LiveDataManager(self.client)

            # Initialize indicators manager
            self.indicators_manager = IndicatorsManager()

            print("✅ Data managers initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing managers: {e}")
            raise

    async def start_data_collection(self):
        """Start the data collection processes with auto-restart capability"""
        try:
            print("🔄 Starting data collection system...")

            # Step 1: Start forex pairs monitoring (background task)
            print("📊 Starting forex pairs monitoring...")
            pairs_task = asyncio.create_task(
                self.pairs_manager.start_monitoring()
            )

            # Step 2: Perform initial historical data collection (one-time)
            print("📈 Performing initial historical data collection...")
            forex_pairs = await self.historical_manager.start_initial_collection()

            if not forex_pairs:
                print("❌ No forex pairs found, cannot start live streaming")
                return

            # Step 3: Calculate initial indicators
            print("📊 Calculating initial technical indicators...")
            await self.indicators_manager.start_indicators_analysis(forex_pairs)

            # Step 4: Link indicators manager to live manager
            self.live_manager.indicators_manager = self.indicators_manager

            # Step 5: Start live data streaming
            print("📡 Starting live data streaming...")
            live_task = asyncio.create_task(
                self.live_manager.start_live_streaming(forex_pairs)
            )

            # Store tasks and forex pairs for monitoring
            self._data_tasks = [pairs_task, live_task]
            self._forex_pairs = forex_pairs

            print("✅ All systems started successfully")
            print("=" * 70)
            print("🔄 Forex pairs monitoring: Running (updates every minute)")
            print("📈 Historical data: Collected (one-time)")
            print("📊 Technical indicators: Calculated (15 indicators)")
            print("📡 Live data streaming: Running (updates every 0.1 seconds)")
            print("🔧 Gap filling: Automatic (when needed)")
            print("🔄 Auto-restart: Enabled (monitors connection)")
            print("=" * 70)

            # Start monitoring and auto-restart loop
            await self.monitor_and_restart_tasks()

        except Exception as e:
            logger.error(f"Error in data collection: {e}")
            raise

    async def monitor_and_restart_tasks(self):
        """Monitor tasks and restart them if they fail"""
        try:
            while self.is_running:
                # Check connection status
                connection_ok = True
                try:
                    # Simple check to see if client is responsive
                    if hasattr(self.client, 'check_connect'):
                        connection_ok = await self.client.check_connect()
                except:
                    connection_ok = False

                # Check if tasks are still running
                pairs_task_running = self._data_tasks[0] and not self._data_tasks[0].done()
                live_task_running = self._data_tasks[1] and not self._data_tasks[1].done()

                # Restart pairs monitoring if stopped
                if not pairs_task_running and self.is_running:
                    print("🔄 Forex pairs monitoring stopped, restarting...")
                    # Reset pairs manager state
                    self.pairs_manager.is_running = False
                    await asyncio.sleep(1)

                    self._data_tasks[0] = asyncio.create_task(
                        self.pairs_manager.start_monitoring()
                    )

                # Restart live streaming if stopped
                if not live_task_running and self.is_running:
                    print("🔄 Live data streaming stopped, restarting...")
                    # Reset live manager state
                    self.live_manager.is_running = False
                    await asyncio.sleep(2)  # Wait for cleanup

                    # Check if we need to update historical data first
                    try:
                        print("📈 Updating historical data after reconnection...")
                        updated_pairs = await self.historical_manager.start_initial_collection()
                        if updated_pairs:
                            self._forex_pairs = updated_pairs

                        # Recalculate indicators
                        print("📊 Recalculating indicators after reconnection...")
                        await self.indicators_manager.start_indicators_analysis(self._forex_pairs)

                    except Exception as e:
                        print(f"⚠️ Error updating data after reconnection: {e}")

                    self._data_tasks[1] = asyncio.create_task(
                        self.live_manager.start_live_streaming(self._forex_pairs)
                    )

                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds

        except Exception as e:
            logger.error(f"Error in task monitoring: {e}")
        finally:
            # Final cleanup
            if hasattr(self, '_data_tasks'):
                for task in self._data_tasks:
                    if task and not task.done():
                        task.cancel()

                await asyncio.gather(*self._data_tasks, return_exceptions=True)

    async def run(self):
        """Main run loop"""
        try:
            self.is_running = True
            print("🚀 Starting Quotex Trading System...")

            # Connect to platform
            if not await self.connect():
                print("❌ Failed to connect to platform, exiting...")
                return False

            # Initialize managers
            await self.initialize_managers()

            # Start data collection
            await self.start_data_collection()

        except KeyboardInterrupt:
            print("\n⌨️ Keyboard interrupt received, shutting down...")
        except Exception as e:
            logger.error(f"Unexpected error in main loop: {e}")
        finally:
            if self.is_running:
                await self.shutdown()

    async def shutdown(self):
        """Gracefully shutdown the system"""
        try:
            print("🔄 Shutting down system...")
            self.is_running = False

            # Stop managers first
            if self.pairs_manager:
                await self.pairs_manager.stop()

            if self.historical_manager:
                await self.historical_manager.stop()

            if self.live_manager:
                await self.live_manager.stop()

            if self.indicators_manager:
                await self.indicators_manager.stop()

            # Cancel data collection tasks
            if hasattr(self, '_data_tasks'):
                for task in self._data_tasks:
                    if task and not task.done():
                        task.cancel()

                # Wait for tasks to complete cancellation
                await asyncio.gather(*self._data_tasks, return_exceptions=True)

            # Close connection
            if self.is_connected:
                await self.client.close()
                print("🔌 Connection closed")

            print("✅ System shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

async def main():
    """Main entry point"""
    try:
        # Create and run the trading system
        system = QuotexTradingSystem()
        await system.run()

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Run the main application
    asyncio.run(main())