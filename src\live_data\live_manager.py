"""
Live Data Manager
Manages real-time data streaming for forex pairs
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from collections import defaultdict

from ..historical_data.gap_filler import GapFiller

logger = logging.getLogger(__name__)

class LiveDataManager:
    """Manages real-time data streaming and integration with historical data"""

    def __init__(self, client, data_dir: str = "data/historical", timeframe: int = 300):
        """
        Initialize the live data manager

        Args:
            client: Quotex client instance
            data_dir: Directory containing historical data files
            timeframe: Timeframe in seconds (default 5 minutes = 300 seconds)
        """
        self.client = client
        self.data_dir = data_dir
        self.timeframe = timeframe
        self.is_running = False
        self.update_interval = 0.1  # Update every 0.1 seconds

        # Live candle data for each pair
        self.live_candles: Dict[str, Dict[str, Any]] = {}

        # Gap filler instance
        self.gap_filler = GapFiller(client, timeframe)

        # Track subscribed pairs
        self.subscribed_pairs: set = set()

        # Indicators manager reference (will be set externally)
        self.indicators_manager = None
        
    def get_pair_file_path(self, pair_name: str) -> str:
        """Get file path for a pair's data"""
        clean_name = pair_name.replace("/", "").replace("\\", "").replace(":", "")
        return os.path.join(self.data_dir, f"{clean_name}.json")
    
    def load_historical_data(self, pair_name: str) -> Dict[str, Any]:
        """Load historical data for a pair"""
        file_path = self.get_pair_file_path(pair_name)
        
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {
                "pair": pair_name,
                "timeframe": self.timeframe,
                "timeframe_minutes": self.timeframe // 60,
                "last_updated": datetime.now().isoformat(),
                "candles_count": 0,
                "candles": [],
                "live_candle": None
            }
        except Exception as e:
            print(f"❌ Error loading historical data for {pair_name}: {e}")
            return {
                "pair": pair_name,
                "timeframe": self.timeframe,
                "timeframe_minutes": self.timeframe // 60,
                "last_updated": datetime.now().isoformat(),
                "candles_count": 0,
                "candles": [],
                "live_candle": None
            }
    
    def save_data_with_live(self, pair_name: str, historical_candles: List[Dict[str, Any]], 
                           live_candle: Optional[Dict[str, Any]] = None) -> bool:
        """Save historical data with live candle"""
        file_path = self.get_pair_file_path(pair_name)
        
        try:
            data = {
                "pair": pair_name,
                "timeframe": self.timeframe,
                "timeframe_minutes": self.timeframe // 60,
                "last_updated": datetime.now().isoformat(),
                "candles_count": len(historical_candles),
                "candles": historical_candles,
                "live_candle": live_candle
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"❌ Error saving data for {pair_name}: {e}")
            return False
    
    def get_candle_start_time(self, timestamp: int) -> int:
        """Get the start time of the candle for a given timestamp"""
        return (timestamp // self.timeframe) * self.timeframe

    def check_expired_candles(self, pair_name: str) -> Dict[str, Any]:
        """Check if current live candle has expired and should be archived"""
        if pair_name not in self.live_candles:
            return None

        live_candle = self.live_candles[pair_name]
        current_time = int(time.time())
        current_candle_start = self.get_candle_start_time(current_time)



        # If current time is in a new candle period, the live candle has expired
        if current_candle_start > live_candle["time"]:
            # Archive the expired candle
            completed_candle = live_candle.copy()
            completed_candle["datetime"] = datetime.fromtimestamp(completed_candle["time"]).strftime('%Y-%m-%d %H:%M:%S')

            # Create new live candle with last known price
            new_readable_time = datetime.fromtimestamp(current_candle_start).strftime('%Y-%m-%d %H:%M:%S')
            last_price = live_candle["close"]

            self.live_candles[pair_name] = {
                "time": current_candle_start,
                "open": last_price,
                "close": last_price,
                "high": last_price,
                "low": last_price,
                "ticks": 1,
                "datetime": new_readable_time,
                "last_update": current_time
            }

            print(f"⏰ {pair_name}: Time-based candle completion - {completed_candle['datetime']} → {new_readable_time}")
            return completed_candle

        return None
    
    def create_live_candle(self, pair_name: str, price: float, timestamp: int) -> Dict[str, Any]:
        """Create or update live candle"""
        candle_start_time = self.get_candle_start_time(timestamp)
        
        # Add readable datetime
        readable_time = datetime.fromtimestamp(candle_start_time).strftime('%Y-%m-%d %H:%M:%S')
        
        if pair_name not in self.live_candles:
            # Create new live candle
            self.live_candles[pair_name] = {
                "time": candle_start_time,
                "open": price,
                "close": price,
                "high": price,
                "low": price,
                "ticks": 1,
                "datetime": readable_time,
                "last_update": timestamp
            }
        else:
            live_candle = self.live_candles[pair_name]
            
            # Check if we need to start a new candle
            if candle_start_time > live_candle["time"]:
                # Current live candle is complete, return it for archiving
                completed_candle = live_candle.copy()
                
                # Start new live candle
                self.live_candles[pair_name] = {
                    "time": candle_start_time,
                    "open": price,
                    "close": price,
                    "high": price,
                    "low": price,
                    "ticks": 1,
                    "datetime": readable_time,
                    "last_update": timestamp
                }
                
                return completed_candle
            else:
                # Update existing live candle
                live_candle["close"] = price
                live_candle["high"] = max(live_candle["high"], price)
                live_candle["low"] = min(live_candle["low"], price)
                live_candle["ticks"] += 1
                live_candle["last_update"] = timestamp
        
        return None  # No completed candle
    
    async def subscribe_to_pair(self, pair_name: str):
        """Subscribe to real-time data for a pair"""
        try:
            if pair_name not in self.subscribed_pairs:
                # Start candles stream for this pair (this also starts price stream)
                self.client.start_candles_stream(pair_name, self.timeframe)

                self.subscribed_pairs.add(pair_name)
                print(f"📡 Subscribed to live data for {pair_name}")

                # Small delay to allow subscription to establish
                await asyncio.sleep(1.0)

        except Exception as e:
            print(f"❌ Error subscribing to {pair_name}: {e}")
    
    async def process_live_data_for_pair(self, pair_name: str):
        """Process live data for a specific pair"""
        try:
            # Load historical data
            data = self.load_historical_data(pair_name)
            historical_candles = data.get("candles", [])

            # Check for gaps in last 10 candles and fill them
            if historical_candles:
                print(f"🔍 Checking for gaps in {pair_name} historical data...")
                historical_candles = await self.gap_filler.fill_gaps(pair_name, historical_candles)

            # Subscribe to live data
            await self.subscribe_to_pair(pair_name)

            last_save_time = 0
            save_interval = 5  # Save every 5 seconds to reduce I/O
            no_data_count = 0
            max_no_data = 50  # Re-subscribe after 50 attempts with no data (5 seconds)

            print(f"🚀 {pair_name}: Starting continuous live monitoring...")

            while self.is_running:
                try:
                    current_time = time.time()

                    # Always check for expired candles (time-based) - this is critical
                    expired_candle = self.check_expired_candles(pair_name)
                    if expired_candle:
                        # Archive expired candle
                        if historical_candles:
                            historical_candles = await self.gap_filler.fill_gap_to_live(
                                pair_name, historical_candles, expired_candle["time"]
                            )

                        historical_candles.append(expired_candle)
                        historical_candles.sort(key=lambda x: x.get('time', 0))

                        print(f"✅ {pair_name}: Expired candle archived - O:{expired_candle['open']:.5f} H:{expired_candle['high']:.5f} L:{expired_candle['low']:.5f} C:{expired_candle['close']:.5f}")

                        # Save and update indicators
                        current_live = self.live_candles.get(pair_name)
                        self.save_data_with_live(pair_name, historical_candles, current_live)

                        if self.indicators_manager:
                            try:
                                await self.indicators_manager.process_pair_indicators(pair_name)
                            except Exception as indicator_error:
                                print(f"⚠️ Indicator error for {pair_name}: {indicator_error}")

                    # Get real-time price data
                    realtime_data = await self.client.get_realtime_price(pair_name)

                    if realtime_data and len(realtime_data) > 0:
                        # Reset no data counter
                        no_data_count = 0

                        # Get the latest price data
                        latest_data = realtime_data[-1]
                        price = latest_data.get("price")
                        timestamp = latest_data.get("time")

                        if price and timestamp:
                            # Create or update live candle
                            completed_candle = self.create_live_candle(pair_name, price, timestamp)

                            # If a candle was completed, add it to historical data
                            if completed_candle:
                                # Check for gaps before adding
                                if historical_candles:
                                    historical_candles = await self.gap_filler.fill_gap_to_live(
                                        pair_name, historical_candles, completed_candle["time"]
                                    )

                                # Add completed candle to historical data
                                historical_candles.append(completed_candle)
                                historical_candles.sort(key=lambda x: x.get('time', 0))

                                print(f"✅ {pair_name}: Completed candle at {completed_candle['datetime']}")

                                # Force save when candle completes
                                current_live = self.live_candles.get(pair_name)
                                self.save_data_with_live(pair_name, historical_candles, current_live)
                                last_save_time = time.time()

                                # Update indicators when candle completes
                                if self.indicators_manager:
                                    await self.indicators_manager.process_pair_indicators(pair_name)
                            else:
                                # Save periodically during live updates
                                current_time = time.time()
                                if current_time - last_save_time >= save_interval:
                                    current_live = self.live_candles.get(pair_name)
                                    self.save_data_with_live(pair_name, historical_candles, current_live)
                                    last_save_time = current_time
                    else:
                        # No data received, increment counter
                        no_data_count += 1
                        if no_data_count >= max_no_data:
                            print(f"🔄 {pair_name}: No data for 5 seconds, re-subscribing...")
                            # Re-subscribe to the pair
                            if pair_name in self.subscribed_pairs:
                                self.subscribed_pairs.remove(pair_name)
                            await self.subscribe_to_pair(pair_name)
                            no_data_count = 0

                        # Save periodically even without new data
                        if current_time - last_save_time >= save_interval:
                            current_live = self.live_candles.get(pair_name)
                            if current_live:
                                self.save_data_with_live(pair_name, historical_candles, current_live)
                                last_save_time = current_time

                except Exception as e:
                    if "Error processing live data" not in str(e):  # Avoid spam
                        print(f"⚠️ {pair_name}: {e}")

                await asyncio.sleep(self.update_interval)

        except Exception as e:
            print(f"❌ Error in live data processing for {pair_name}: {e}")
    
    async def start_live_streaming(self, pairs_list: List[str]):
        """Start live streaming for all pairs"""
        try:
            self.is_running = True
            print(f"🚀 Starting live data streaming for {len(pairs_list)} pairs...")
            
            # Create tasks for all pairs
            tasks = []
            for pair_name in pairs_list:
                task = asyncio.create_task(self.process_live_data_for_pair(pair_name))
                tasks.append(task)
            
            # Store tasks for cleanup
            self._live_tasks = tasks
            
            print("📡 Live streaming started for all pairs")
            
            # Wait for all tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            print(f"❌ Error in live streaming: {e}")
        finally:
            self.is_running = False
            print("🛑 Live data streaming stopped")
    
    async def stop(self):
        """Stop live streaming"""
        print("🛑 Stopping live data streaming...")
        self.is_running = False
        
        # Cancel live tasks
        if hasattr(self, '_live_tasks'):
            for task in self._live_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete cancellation
            await asyncio.gather(*self._live_tasks, return_exceptions=True)
        
        # Unsubscribe from all pairs
        for pair_name in self.subscribed_pairs:
            try:
                self.client.stop_candles_stream(pair_name)
            except:
                pass
        
        self.subscribed_pairs.clear()
        print("✅ Live data streaming stopped")
