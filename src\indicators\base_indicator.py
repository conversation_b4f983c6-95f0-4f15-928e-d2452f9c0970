"""
Base Indicator Class
Abstract base class for all technical indicators
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
import numpy as np
from datetime import datetime

class BaseIndicator(ABC):
    """Abstract base class for all technical indicators"""
    
    def __init__(self, name: str, parameters: Dict[str, Any] = None):
        """
        Initialize base indicator
        
        Args:
            name: Name of the indicator
            parameters: Dictionary of indicator parameters
        """
        self.name = name
        self.parameters = parameters or {}
        self.values: List[float] = []
        self.timestamps: List[int] = []
        self.is_ready = False
        
    @abstractmethod
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate indicator values for given candles
        
        Args:
            candles: List of candle data
            
        Returns:
            Dictionary containing calculated values
        """
        pass
    
    @abstractmethod
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update indicator with live candle data
        
        Args:
            live_candle: Current live candle data
            historical_values: Previously calculated historical values
            
        Returns:
            Updated indicator values
        """
        pass
    
    @abstractmethod
    def get_required_periods(self) -> int:
        """
        Get minimum number of periods required for calculation
        
        Returns:
            Minimum periods needed
        """
        pass
    
    def extract_prices(self, candles: List[Dict[str, Any]], price_type: str = 'close') -> List[float]:
        """
        Extract price data from candles
        
        Args:
            candles: List of candle data
            price_type: Type of price ('open', 'high', 'low', 'close')
            
        Returns:
            List of prices
        """
        return [float(candle.get(price_type, 0)) for candle in candles]
    
    def extract_ohlc(self, candles: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        Extract OHLC data from candles
        
        Args:
            candles: List of candle data
            
        Returns:
            Dictionary with OHLC lists
        """
        return {
            'open': self.extract_prices(candles, 'open'),
            'high': self.extract_prices(candles, 'high'),
            'low': self.extract_prices(candles, 'low'),
            'close': self.extract_prices(candles, 'close'),
            'timestamps': [candle.get('time', 0) for candle in candles]
        }
    
    def validate_data(self, candles: List[Dict[str, Any]]) -> bool:
        """
        Validate input data
        
        Args:
            candles: List of candle data
            
        Returns:
            True if data is valid
        """
        if not candles:
            return False
        
        required_periods = self.get_required_periods()
        if len(candles) < required_periods:
            return False
        
        # Check if candles have required fields
        required_fields = ['open', 'high', 'low', 'close', 'time']
        for candle in candles[-required_periods:]:
            for field in required_fields:
                if field not in candle:
                    return False
        
        return True
    
    def format_result(self, values: Union[float, List[float], Dict[str, Any]], 
                     timestamp: int = None) -> Dict[str, Any]:
        """
        Format indicator result
        
        Args:
            values: Calculated values
            timestamp: Current timestamp
            
        Returns:
            Formatted result dictionary
        """
        result = {
            'indicator': self.name,
            'parameters': self.parameters,
            'timestamp': timestamp or int(datetime.now().timestamp()),
            'datetime': datetime.fromtimestamp(timestamp or int(datetime.now().timestamp())).strftime('%Y-%m-%d %H:%M:%S')
        }
        
        if isinstance(values, dict):
            result.update(values)
        elif isinstance(values, list):
            result['values'] = values
            result['current'] = values[-1] if values else None
        else:
            result['value'] = values
            result['current'] = values
        
        return result
    
    def calculate_sma(self, prices: List[float], period: int) -> List[float]:
        """
        Calculate Simple Moving Average
        
        Args:
            prices: List of prices
            period: Period for SMA
            
        Returns:
            List of SMA values
        """
        if len(prices) < period:
            return []
        
        sma_values = []
        for i in range(period - 1, len(prices)):
            sma = sum(prices[i - period + 1:i + 1]) / period
            sma_values.append(sma)
        
        return sma_values
    
    def calculate_ema(self, prices: List[float], period: int) -> List[float]:
        """
        Calculate Exponential Moving Average
        
        Args:
            prices: List of prices
            period: Period for EMA
            
        Returns:
            List of EMA values
        """
        if len(prices) < period:
            return []
        
        multiplier = 2 / (period + 1)
        ema_values = []
        
        # First EMA is SMA
        first_sma = sum(prices[:period]) / period
        ema_values.append(first_sma)
        
        # Calculate subsequent EMAs
        for i in range(period, len(prices)):
            ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return ema_values
    
    def calculate_true_range(self, high: List[float], low: List[float], close: List[float]) -> List[float]:
        """
        Calculate True Range
        
        Args:
            high: List of high prices
            low: List of low prices
            close: List of close prices
            
        Returns:
            List of True Range values
        """
        if len(high) < 2 or len(low) < 2 or len(close) < 2:
            return []
        
        tr_values = []
        for i in range(1, len(high)):
            tr1 = high[i] - low[i]
            tr2 = abs(high[i] - close[i-1])
            tr3 = abs(low[i] - close[i-1])
            tr = max(tr1, tr2, tr3)
            tr_values.append(tr)
        
        return tr_values
    
    def calculate_rsi(self, prices: List[float], period: int) -> List[float]:
        """
        Calculate Relative Strength Index
        
        Args:
            prices: List of prices
            period: Period for RSI
            
        Returns:
            List of RSI values
        """
        if len(prices) < period + 1:
            return []
        
        # Calculate price changes
        changes = []
        for i in range(1, len(prices)):
            changes.append(prices[i] - prices[i-1])
        
        # Separate gains and losses
        gains = [max(change, 0) for change in changes]
        losses = [abs(min(change, 0)) for change in changes]
        
        rsi_values = []
        
        # Calculate first RSI using SMA
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        if avg_loss == 0:
            rsi_values.append(100)
        else:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            rsi_values.append(rsi)
        
        # Calculate subsequent RSI using smoothed averages
        for i in range(period, len(changes)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            if avg_loss == 0:
                rsi_values.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
                rsi_values.append(rsi)
        
        return rsi_values
