"""
Heiken Ashi Indicator
Heiken Ashi candlesticks
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class <PERSON>ikenAs<PERSON>(BaseIndicator):
    """Heiken Ashi candlesticks"""
    
    def __init__(self):
        super().__init__("Heiken<PERSON><PERSON>", {})
    
    def get_required_periods(self) -> int:
        return 2  # Need at least 2 candles
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result({}, candles[-1].get('time') if candles else None)
        
        ohlc = self.extract_ohlc(candles)
        
        ha_open = []
        ha_high = []
        ha_low = []
        ha_close = []
        
        for i in range(len(candles)):
            # HA Close = (O + H + L + C) / 4
            ha_c = (ohlc['open'][i] + ohlc['high'][i] + ohlc['low'][i] + ohlc['close'][i]) / 4
            ha_close.append(ha_c)
            
            if i == 0:
                # First HA Open = (O + C) / 2
                ha_o = (ohlc['open'][i] + ohlc['close'][i]) / 2
            else:
                # HA Open = (previous HA Open + previous HA Close) / 2
                ha_o = (ha_open[i-1] + ha_close[i-1]) / 2
            
            ha_open.append(ha_o)
            
            # HA High = max(H, HA Open, HA Close)
            ha_h = max(ohlc['high'][i], ha_o, ha_c)
            ha_high.append(ha_h)
            
            # HA Low = min(L, HA Open, HA Close)
            ha_l = min(ohlc['low'][i], ha_o, ha_c)
            ha_low.append(ha_l)
        
        return self.format_result({
            'ha_open': ha_open,
            'ha_high': ha_high,
            'ha_low': ha_low,
            'ha_close': ha_close,
            'current_open': ha_open[-1] if ha_open else None,
            'current_high': ha_high[-1] if ha_high else None,
            'current_low': ha_low[-1] if ha_low else None,
            'current_close': ha_close[-1] if ha_close else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('ha_open'):
            return self.format_result({}, live_candle.get('time'))
        
        # Calculate live HA values
        live_open = float(live_candle.get('open', 0))
        live_high = float(live_candle.get('high', 0))
        live_low = float(live_candle.get('low', 0))
        live_close = float(live_candle.get('close', 0))
        
        # HA Close = (O + H + L + C) / 4
        ha_close = (live_open + live_high + live_low + live_close) / 4
        
        # HA Open = (previous HA Open + previous HA Close) / 2
        prev_ha_open = historical_values['ha_open'][-1]
        prev_ha_close = historical_values['ha_close'][-1]
        ha_open = (prev_ha_open + prev_ha_close) / 2
        
        # HA High = max(H, HA Open, HA Close)
        ha_high = max(live_high, ha_open, ha_close)
        
        # HA Low = min(L, HA Open, HA Close)
        ha_low = min(live_low, ha_open, ha_close)
        
        return self.format_result({
            'ha_open': historical_values['ha_open'] + [ha_open],
            'ha_high': historical_values['ha_high'] + [ha_high],
            'ha_low': historical_values['ha_low'] + [ha_low],
            'ha_close': historical_values['ha_close'] + [ha_close],
            'current_open': ha_open,
            'current_high': ha_high,
            'current_low': ha_low,
            'current_close': ha_close,
            'live': True
        }, live_candle.get('time'))
