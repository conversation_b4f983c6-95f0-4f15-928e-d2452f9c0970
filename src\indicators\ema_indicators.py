"""
EMA Indicators
Exponential Moving Average indicators with different periods
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class EMA5(BaseIndicator):
    """Exponential Moving Average with 5 periods"""
    
    def __init__(self):
        super().__init__("EMA5", {"period": 5})
    
    def get_required_periods(self) -> int:
        return 5
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        ema_values = self.calculate_ema(prices, 5)
        
        return self.format_result({
            'values': ema_values,
            'current': ema_values[-1] if ema_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        last_ema = historical_values['values'][-1]
        current_price = float(live_candle.get('close', 0))
        multiplier = 2 / (5 + 1)
        
        live_ema = (current_price * multiplier) + (last_ema * (1 - multiplier))
        
        return self.format_result({
            'values': historical_values['values'] + [live_ema],
            'current': live_ema,
            'live': True
        }, live_candle.get('time'))

class EMA10(BaseIndicator):
    """Exponential Moving Average with 10 periods"""
    
    def __init__(self):
        super().__init__("EMA10", {"period": 10})
    
    def get_required_periods(self) -> int:
        return 10
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        ema_values = self.calculate_ema(prices, 10)
        
        return self.format_result({
            'values': ema_values,
            'current': ema_values[-1] if ema_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        last_ema = historical_values['values'][-1]
        current_price = float(live_candle.get('close', 0))
        multiplier = 2 / (10 + 1)
        
        live_ema = (current_price * multiplier) + (last_ema * (1 - multiplier))
        
        return self.format_result({
            'values': historical_values['values'] + [live_ema],
            'current': live_ema,
            'live': True
        }, live_candle.get('time'))

class EMA21(BaseIndicator):
    """Exponential Moving Average with 21 periods"""
    
    def __init__(self):
        super().__init__("EMA21", {"period": 21})
    
    def get_required_periods(self) -> int:
        return 21
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        ema_values = self.calculate_ema(prices, 21)
        
        return self.format_result({
            'values': ema_values,
            'current': ema_values[-1] if ema_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        last_ema = historical_values['values'][-1]
        current_price = float(live_candle.get('close', 0))
        multiplier = 2 / (21 + 1)
        
        live_ema = (current_price * multiplier) + (last_ema * (1 - multiplier))
        
        return self.format_result({
            'values': historical_values['values'] + [live_ema],
            'current': live_ema,
            'live': True
        }, live_candle.get('time'))
