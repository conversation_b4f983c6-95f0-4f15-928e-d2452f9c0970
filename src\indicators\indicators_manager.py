"""
Indicators Manager
Manages all technical indicators calculation and storage
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

# Import all indicators
from .ema_indicators import EMA5, EMA10, EMA21
from .sma_indicators import SMA10
from .rsi_indicators import RSI5, RSI14
from .macd_indicator import MACD
from .momentum_indicator import Momentum10
from .bollinger_indicator import BollingerBands
from .atr_indicators import ATR5, ATR14
from .heiken_ashi_indicator import <PERSON>iken<PERSON><PERSON>
from .zscore_indicator import ZScore

logger = logging.getLogger(__name__)

class IndicatorsManager:
    """Manages all technical indicators for forex pairs"""
    
    def __init__(self, data_dir: str = "data/indicators"):
        """
        Initialize the indicators manager
        
        Args:
            data_dir: Directory to store indicators data
        """
        self.data_dir = data_dir
        self.is_running = False
        
        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize all indicators
        self.indicators = {
            'EMA5': EMA5(),
            'EMA10': EMA10(),
            'EMA21': EMA21(),
            'SMA10': SMA10(),
            'RSI5': RSI5(),
            'RSI14': RSI14(),
            'MACD': MACD(),
            'Momentum10': Momentum10(),
            'BollingerBands': BollingerBands(),
            'ATR5': ATR5(),
            'ATR14': ATR14(),
            'HeikenAshi': HeikenAshi(),
            'ZScore': ZScore()
        }
        
        print(f"📊 Initialized {len(self.indicators)} technical indicators")
    
    def get_pair_indicators_file_path(self, pair_name: str) -> str:
        """Get file path for a pair's indicators data"""
        clean_name = pair_name.replace("/", "").replace("\\", "").replace(":", "")
        return os.path.join(self.data_dir, f"{clean_name}_indicators.json")
    
    def load_historical_data(self, pair_name: str) -> Dict[str, Any]:
        """Load historical data for a pair from the historical data directory"""
        historical_dir = "data/historical"
        clean_name = pair_name.replace("/", "").replace("\\", "").replace(":", "")
        file_path = os.path.join(historical_dir, f"{clean_name}.json")
        
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"❌ Error loading historical data for {pair_name}: {e}")
            return {}
    
    def save_indicators_data(self, pair_name: str, indicators_data: Dict[str, Any]) -> bool:
        """Save indicators data for a pair"""
        file_path = self.get_pair_indicators_file_path(pair_name)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(indicators_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ Error saving indicators data for {pair_name}: {e}")
            return False
    
    def load_indicators_data(self, pair_name: str) -> Dict[str, Any]:
        """Load existing indicators data for a pair"""
        file_path = self.get_pair_indicators_file_path(pair_name)
        
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"❌ Error loading indicators data for {pair_name}: {e}")
            return {}
    
    def calculate_all_indicators(self, pair_name: str, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate all indicators for given candles"""
        if not candles:
            return {}
        
        results = {}
        
        for indicator_name, indicator in self.indicators.items():
            try:
                result = indicator.calculate(candles)
                results[indicator_name] = result
            except Exception as e:
                print(f"⚠️ Error calculating {indicator_name} for {pair_name}: {e}")
                results[indicator_name] = {}
        
        return results
    
    def update_live_indicators(self, pair_name: str, live_candle: Dict[str, Any], 
                              historical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Update indicators with live candle data"""
        if not live_candle:
            return historical_indicators
        
        live_results = {}
        
        for indicator_name, indicator in self.indicators.items():
            try:
                historical_values = historical_indicators.get(indicator_name, {})
                live_result = indicator.update_live(live_candle, historical_values)
                live_results[indicator_name] = live_result
            except Exception as e:
                print(f"⚠️ Error updating live {indicator_name} for {pair_name}: {e}")
                live_results[indicator_name] = historical_indicators.get(indicator_name, {})
        
        return live_results
    
    async def process_pair_indicators(self, pair_name: str):
        """Process indicators for a specific pair"""
        try:
            print(f"📊 Processing indicators for {pair_name}...")
            
            # Load historical data
            historical_data = self.load_historical_data(pair_name)
            candles = historical_data.get('candles', [])
            live_candle = historical_data.get('live_candle')
            
            if not candles:
                print(f"⚠️ No historical data found for {pair_name}")
                return
            
            # Calculate indicators for historical data
            indicators_results = self.calculate_all_indicators(pair_name, candles)
            
            # Update with live data if available
            if live_candle:
                indicators_results = self.update_live_indicators(pair_name, live_candle, indicators_results)
            
            # Prepare final data structure
            indicators_data = {
                'pair': pair_name,
                'last_updated': datetime.now().isoformat(),
                'candles_count': len(candles),
                'has_live_data': live_candle is not None,
                'indicators': indicators_results
            }
            
            # Save indicators data
            self.save_indicators_data(pair_name, indicators_data)
            
            # Count successful indicators
            successful_indicators = sum(1 for result in indicators_results.values() 
                                      if result and result.get('current') is not None)
            
            print(f"✅ {pair_name}: {successful_indicators}/{len(self.indicators)} indicators calculated")
            
        except Exception as e:
            print(f"❌ Error processing indicators for {pair_name}: {e}")
    
    async def process_all_pairs_indicators(self, pairs_list: List[str]):
        """Process indicators for all pairs in parallel"""
        try:
            print(f"🚀 Processing indicators for {len(pairs_list)} pairs...")
            print("=" * 60)
            
            # Create tasks for all pairs
            tasks = [self.process_pair_indicators(pair) for pair in pairs_list]
            
            # Execute all tasks
            await asyncio.gather(*tasks, return_exceptions=True)
            
            print("=" * 60)
            print("✅ Indicators processing completed for all pairs")
            
        except Exception as e:
            print(f"❌ Error processing indicators: {e}")
    
    async def start_indicators_analysis(self, pairs_list: List[str]):
        """Start indicators analysis for all pairs"""
        try:
            self.is_running = True
            print("📊 Starting technical indicators analysis...")
            
            # Initial calculation for all pairs
            await self.process_all_pairs_indicators(pairs_list)
            
            print("✅ Initial indicators analysis completed")
            
        except Exception as e:
            print(f"❌ Error in indicators analysis: {e}")
        finally:
            self.is_running = False
    
    async def stop(self):
        """Stop indicators analysis"""
        print("🛑 Stopping indicators analysis...")
        self.is_running = False
    
    def get_pair_indicators(self, pair_name: str) -> Optional[Dict[str, Any]]:
        """Get indicators data for a specific pair"""
        return self.load_indicators_data(pair_name)
    
    def get_indicator_summary(self, pair_name: str) -> Dict[str, Any]:
        """Get summary of current indicator values for a pair"""
        indicators_data = self.load_indicators_data(pair_name)
        
        if not indicators_data or 'indicators' not in indicators_data:
            return {}
        
        summary = {
            'pair': pair_name,
            'last_updated': indicators_data.get('last_updated'),
            'current_values': {}
        }
        
        for indicator_name, indicator_data in indicators_data['indicators'].items():
            current_value = indicator_data.get('current')
            if current_value is not None:
                summary['current_values'][indicator_name] = current_value
        
        return summary
