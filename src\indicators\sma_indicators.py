"""
SMA Indicators
Simple Moving Average indicators
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class SMA10(BaseIndicator):
    """Simple Moving Average with 10 periods"""
    
    def __init__(self):
        super().__init__("SMA10", {"period": 10})
    
    def get_required_periods(self) -> int:
        return 10
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        sma_values = self.calculate_sma(prices, 10)
        
        return self.format_result({
            'values': sma_values,
            'current': sma_values[-1] if sma_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values') or len(historical_values['values']) < 9:
            return self.format_result(None, live_candle.get('time'))
        
        # For live SMA, we need the last 9 historical prices + current live price
        # This is a simplified approach - in practice, you'd need access to the last 9 candle prices
        current_price = float(live_candle.get('close', 0))
        
        # Approximate live SMA (this would need more historical price data for accuracy)
        last_sma = historical_values['values'][-1]
        live_sma = last_sma  # Simplified - would need proper calculation with sliding window
        
        return self.format_result({
            'values': historical_values['values'] + [live_sma],
            'current': live_sma,
            'live': True
        }, live_candle.get('time'))
