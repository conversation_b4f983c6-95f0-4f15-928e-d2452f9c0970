"""
RSI Indicators
Relative Strength Index indicators with different periods
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class RSI5(BaseIndicator):
    """Relative Strength Index with 5 periods"""
    
    def __init__(self):
        super().__init__("RSI5", {"period": 5})
    
    def get_required_periods(self) -> int:
        return 6  # Need one extra for price change calculation
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        rsi_values = self.calculate_rsi(prices, 5)
        
        return self.format_result({
            'values': rsi_values,
            'current': rsi_values[-1] if rsi_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live RSI calculation, we would need the previous price and current gains/losses
        # This is a simplified approach
        current_rsi = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_rsi],
            'current': current_rsi,
            'live': True
        }, live_candle.get('time'))

class RSI14(BaseIndicator):
    """Relative Strength Index with 14 periods"""
    
    def __init__(self):
        super().__init__("RSI14", {"period": 14})
    
    def get_required_periods(self) -> int:
        return 15  # Need one extra for price change calculation
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        rsi_values = self.calculate_rsi(prices, 14)
        
        return self.format_result({
            'values': rsi_values,
            'current': rsi_values[-1] if rsi_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live RSI calculation, we would need the previous price and current gains/losses
        # This is a simplified approach
        current_rsi = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_rsi],
            'current': current_rsi,
            'live': True
        }, live_candle.get('time'))
