"""
Momentum Indicator
Price momentum with 10 periods
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class Momentum10(BaseIndicator):
    """Momentum with 10 periods"""
    
    def __init__(self):
        super().__init__("Momentum10", {"period": 10})
    
    def get_required_periods(self) -> int:
        return 11  # Need current + 10 periods back
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        momentum_values = []
        
        # Calculate momentum: current price / price n periods ago
        for i in range(10, len(prices)):
            momentum = (prices[i] / prices[i - 10]) * 100
            momentum_values.append(momentum)
        
        return self.format_result({
            'values': momentum_values,
            'current': momentum_values[-1] if momentum_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live momentum, we would need the price from 10 periods ago
        # This is a simplified approach
        current_momentum = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_momentum],
            'current': current_momentum,
            'live': True
        }, live_candle.get('time'))
