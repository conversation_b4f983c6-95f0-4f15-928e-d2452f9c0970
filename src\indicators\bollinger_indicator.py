"""
Bollinger Bands Indicator
Bollinger Bands with 20 periods and 2 standard deviations
"""

from typing import List, Dict, Any
import statistics
from .base_indicator import BaseIndicator

class BollingerBands(BaseIndicator):
    """Bollinger Bands with 20 periods and 2 standard deviations"""
    
    def __init__(self):
        super().__init__("BollingerBands", {"period": 20, "std_dev": 2})
    
    def get_required_periods(self) -> int:
        return 20
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result({}, candles[-1].get('time') if candles else None)
        
        prices = self.extract_prices(candles, 'close')
        
        # Calculate SMA (middle band)
        sma_values = self.calculate_sma(prices, 20)
        
        if not sma_values:
            return self.format_result({}, candles[-1].get('time'))
        
        upper_bands = []
        lower_bands = []
        
        # Calculate bands
        for i in range(len(sma_values)):
            # Get the 20 prices for this period
            period_prices = prices[i:i + 20]
            
            # Calculate standard deviation
            std_dev = statistics.stdev(period_prices)
            
            # Calculate bands
            middle = sma_values[i]
            upper = middle + (2 * std_dev)
            lower = middle - (2 * std_dev)
            
            upper_bands.append(upper)
            lower_bands.append(lower)
        
        return self.format_result({
            'upper_band': upper_bands,
            'middle_band': sma_values,
            'lower_band': lower_bands,
            'current_upper': upper_bands[-1] if upper_bands else None,
            'current_middle': sma_values[-1] if sma_values else None,
            'current_lower': lower_bands[-1] if lower_bands else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('middle_band'):
            return self.format_result({}, live_candle.get('time'))
        
        # For live Bollinger Bands, we would need to recalculate with live price
        # This is a simplified approach
        current_upper = historical_values.get('current_upper', 0)
        current_middle = historical_values.get('current_middle', 0)
        current_lower = historical_values.get('current_lower', 0)
        
        return self.format_result({
            'upper_band': historical_values['upper_band'] + [current_upper],
            'middle_band': historical_values['middle_band'] + [current_middle],
            'lower_band': historical_values['lower_band'] + [current_lower],
            'current_upper': current_upper,
            'current_middle': current_middle,
            'current_lower': current_lower,
            'live': True
        }, live_candle.get('time'))
