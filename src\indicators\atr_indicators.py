"""
ATR Indicators
Average True Range indicators with different periods
"""

from typing import List, Dict, Any
from .base_indicator import BaseIndicator

class ATR5(BaseIndicator):
    """Average True Range with 5 periods"""
    
    def __init__(self):
        super().__init__("ATR5", {"period": 5})
    
    def get_required_periods(self) -> int:
        return 6  # Need one extra for TR calculation
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        ohlc = self.extract_ohlc(candles)
        
        # Calculate True Range
        tr_values = self.calculate_true_range(ohlc['high'], ohlc['low'], ohlc['close'])
        
        if not tr_values:
            return self.format_result([], candles[-1].get('time'))
        
        # Calculate ATR (SMA of True Range)
        atr_values = self.calculate_sma(tr_values, 5)
        
        return self.format_result({
            'values': atr_values,
            'current': atr_values[-1] if atr_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live ATR, we would need to calculate current TR and update the average
        # This is a simplified approach
        current_atr = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_atr],
            'current': current_atr,
            'live': True
        }, live_candle.get('time'))

class ATR14(BaseIndicator):
    """Average True Range with 14 periods"""
    
    def __init__(self):
        super().__init__("ATR14", {"period": 14})
    
    def get_required_periods(self) -> int:
        return 15  # Need one extra for TR calculation
    
    def calculate(self, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not self.validate_data(candles):
            return self.format_result([], candles[-1].get('time') if candles else None)
        
        ohlc = self.extract_ohlc(candles)
        
        # Calculate True Range
        tr_values = self.calculate_true_range(ohlc['high'], ohlc['low'], ohlc['close'])
        
        if not tr_values:
            return self.format_result([], candles[-1].get('time'))
        
        # Calculate ATR (SMA of True Range)
        atr_values = self.calculate_sma(tr_values, 14)
        
        return self.format_result({
            'values': atr_values,
            'current': atr_values[-1] if atr_values else None
        }, candles[-1].get('time'))
    
    def update_live(self, live_candle: Dict[str, Any], historical_values: Dict[str, Any]) -> Dict[str, Any]:
        if not historical_values.get('values'):
            return self.format_result(None, live_candle.get('time'))
        
        # For live ATR, we would need to calculate current TR and update the average
        # This is a simplified approach
        current_atr = historical_values['values'][-1]
        
        return self.format_result({
            'values': historical_values['values'] + [current_atr],
            'current': current_atr,
            'live': True
        }, live_candle.get('time'))
